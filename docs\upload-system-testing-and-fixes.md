# Upload System Testing and Fixes

## Issues Identified During Testing

### 1. Firebase Storage Configuration Error
**Error**: `Firebase Storage: An unknown error occurred, please check the error payload for server response. (storage/unknown)`
**Status Code**: 404
**Root Cause**: Firebase Storage bucket configuration issue or missing environment variables

### 2. Upload Failure Cascade
**Error**: `Save failed: 400 Bad Request`
**Root Cause**: When Firebase upload fails, the 360° record creation fails because there's no valid URL to save

## Solutions Implemented

### 1. **Temporary Local Storage Fallback** (`src/lib/file-upload.js`)

Added back local storage fallback for development testing while Firebase issues are resolved:

```javascript
export async function uploadFile(file, folder = 'general', filename = null) {
  try {
    // Try Firebase Storage first
    const storageRef = ref(storage, filePath);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return {
      success: true,
      url: downloadURL,
      storage: 'firebase'
    };
  } catch (firebaseError) {
    console.warn('Firebase upload failed, falling back to local storage:', firebaseError);
    
    // Fallback to local storage for development
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', folder);
    fs.mkdirSync(uploadsDir, { recursive: true });
    
    const localPath = path.join(uploadsDir, filename);
    const buffer = await file.arrayBuffer();
    fs.writeFileSync(localPath, Buffer.from(buffer));
    
    const publicUrl = `/uploads/${folder}/${filename}`;
    
    return {
      success: true,
      url: publicUrl,
      storage: 'local'
    };
  }
}
```

### 2. **Enhanced Error Logging** (`src/app/api/360s/route.js`)

Added comprehensive logging to the 360° creation endpoint:

```javascript
export async function POST(request) {
  try {
    console.log('360° POST request received');
    const body = await request.json();
    console.log('Request body received:', JSON.stringify(body, null, 2));
    
    // Enhanced validation with detailed error messages
    const requiredFields = ['name', 'url'];
    for (const field of requiredFields) {
      if (!body[field]) {
        console.error(`Validation failed: ${field} is required`);
        return NextResponse.json({
          success: false,
          error: 'Validation Error',
          message: `${field} is required`,
          receivedData: body,
        }, { status: 400 });
      }
    }
    
    // Clean and validate data before saving
    const cleanData = {
      name: body.name,
      url: body.url,
      originalFileName: body.originalFileName || '',
      priority: Number(body.priority) || 0,
      markerList: body.markerList || [],
      cameraPosition: Number(body.cameraPosition) || -0.0001,
      _360Rotation: Number(body._360Rotation) || -0.0001,
    };
    
    console.log('Clean data for saving:', JSON.stringify(cleanData, null, 2));
    
    const new360 = new _360Settings(cleanData);
    await new360.save();
    
    console.log('360° created successfully:', new360._id);
    
    return NextResponse.json({
      success: true,
      data: new360,
      message: '360 created successfully',
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating 360:', error);
    console.error('Error stack:', error.stack);
    
    if (error.name === 'ValidationError') {
      console.error('Mongoose validation error:', error.errors);
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        details: error.errors,
      }, { status: 400 });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Failed to create 360',
      message: error.message,
      stack: error.stack,
    }, { status: 500 });
  }
}
```

### 3. **Frontend Error Handling** (`src/components/360s-manager/360Form.jsx`)

Enhanced error handling in the frontend to capture detailed error information:

```javascript
if (!saveResponse.ok) {
  const errorText = await saveResponse.text();
  console.error('Save response error:', {
    status: saveResponse.status,
    statusText: saveResponse.statusText,
    body: errorText,
    file: file.name,
    sentData: threeSixtyData
  });
  
  // Try to parse the error response
  let errorDetails = errorText;
  try {
    const errorJson = JSON.parse(errorText);
    errorDetails = errorJson.message || errorJson.error || errorText;
    console.error('Parsed error details:', errorJson);
  } catch (parseError) {
    console.error('Could not parse error response as JSON');
  }
  
  throw new Error(`Save failed: ${saveResponse.status} ${saveResponse.statusText} - ${errorDetails}`);
}
```

### 4. **Firebase Configuration Test Endpoint** (`src/app/api/test-firebase/route.js`)

Created a diagnostic endpoint to test Firebase configuration:

```javascript
export async function GET(request) {
  try {
    // Check environment variables
    const firebaseConfig = {
      apiKey: process.env.FIREBASE_API_KEY ? '***SET***' : 'NOT SET',
      authDomain: process.env.FIREBASE_AUTH_DOMAIN ? '***SET***' : 'NOT SET',
      projectId: process.env.FIREBASE_PROJECT_ID ? '***SET***' : 'NOT SET',
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET ? '***SET***' : 'NOT SET',
      messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID ? '***SET***' : 'NOT SET',
      appId: process.env.FIREBASE_APP_ID ? '***SET***' : 'NOT SET',
    };
    
    // Test storage access
    const storageRef = ref(storage, 'elephantisland/');
    const result = await listAll(storageRef);
    
    return NextResponse.json({
      success: true,
      message: 'Firebase Storage is working correctly',
      config: firebaseConfig,
      itemsFound: result.items.length,
      foldersFound: result.prefixes.length
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
      code: error.code,
      config: firebaseConfig
    }, { status: 500 });
  }
}
```

## Testing Results

### Current Status
- ✅ **Upload Validation**: File type and size validation working
- ✅ **Local Storage Fallback**: Working when Firebase fails
- ✅ **Error Logging**: Comprehensive logging implemented
- ❌ **Firebase Storage**: Configuration issue needs resolution
- ✅ **Database Operations**: 360° record creation working with local URLs

### Error Patterns Observed
1. **Firebase 404 Error**: Suggests bucket doesn't exist or wrong configuration
2. **Cascade Failures**: Upload failure causes database save failure
3. **Environment Variables**: May not be properly configured for Firebase

## Next Steps

### 1. **Firebase Configuration Resolution**
- Verify Firebase project setup
- Check Storage bucket creation
- Validate environment variables
- Test Firebase console access

### 2. **Production Deployment Strategy**
- Ensure Firebase works in production
- Remove local storage fallback for production
- Add environment-specific configuration

### 3. **Error Recovery**
- Implement retry logic for Firebase uploads
- Add user-friendly error messages
- Provide upload progress indicators

## Environment Variables Required

```env
# Firebase Configuration
FIREBASE_API_KEY=your_api_key
FIREBASE_AUTH_DOMAIN=your_domain
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_bucket
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id
```

## Testing Endpoints

1. **Firebase Test**: `GET /api/test-firebase`
2. **Upload Test**: `POST /api/upload/360s`
3. **360° Creation**: `POST /api/360s`

## Commit Message
Fix upload system with local storage fallback, enhanced error logging, and Firebase configuration testing for development environment
