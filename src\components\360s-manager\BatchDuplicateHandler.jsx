'use client';

import { useState, useEffect } from 'react';
import DuplicateConfirmationModal from './DuplicateConfirmationModal';

export default function BatchDuplicateHandler({ 
  duplicates, 
  onAllResolved, 
  onCancel,
  isOpen 
}) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [resolutions, setResolutions] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);

  // Reset when duplicates change
  useEffect(() => {
    if (duplicates && duplicates.length > 0) {
      setCurrentIndex(0);
      setResolutions([]);
    }
  }, [duplicates]);

  if (!isOpen || !duplicates || duplicates.length === 0) {
    return null;
  }

  const currentDuplicate = duplicates[currentIndex];
  const isLastDuplicate = currentIndex === duplicates.length - 1;

  const handleConfirm = async (duplicateInfo) => {
    setIsProcessing(true);
    
    // Add resolution to list
    const newResolution = {
      ...duplicateInfo,
      action: 'replace',
      timestamp: new Date().toISOString()
    };
    
    const updatedResolutions = [...resolutions, newResolution];
    setResolutions(updatedResolutions);
    
    if (isLastDuplicate) {
      // All duplicates resolved
      await onAllResolved(updatedResolutions);
    } else {
      // Move to next duplicate
      setCurrentIndex(prev => prev + 1);
    }
    
    setIsProcessing(false);
  };

  const handleSkip = (duplicateInfo) => {
    // Add skip resolution to list
    const newResolution = {
      ...duplicateInfo,
      action: 'skip',
      timestamp: new Date().toISOString()
    };
    
    const updatedResolutions = [...resolutions, newResolution];
    setResolutions(updatedResolutions);
    
    if (isLastDuplicate) {
      // All duplicates resolved
      onAllResolved(updatedResolutions);
    } else {
      // Move to next duplicate
      setCurrentIndex(prev => prev + 1);
    }
  };

  const handleClose = () => {
    if (!isProcessing) {
      onCancel();
    }
  };

  return (
    <>
      {/* Progress Indicator */}
      {duplicates.length > 1 && (
        <div className="fixed top-4 right-4 bg-white rounded-lg shadow-lg p-4 z-40 border border-gray-200">
          <div className="text-sm text-gray-600 mb-2">
            Resolving Duplicates
          </div>
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-gray-900">
              {currentIndex + 1} of {duplicates.length}
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-2 min-w-[100px]">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentIndex + 1) / duplicates.length) * 100}%` }}
              />
            </div>
          </div>
          {resolutions.length > 0 && (
            <div className="mt-2 text-xs text-gray-500">
              Resolved: {resolutions.filter(r => r.action === 'replace').length} replaced, {resolutions.filter(r => r.action === 'skip').length} skipped
            </div>
          )}
        </div>
      )}

      {/* Duplicate Confirmation Modal */}
      <DuplicateConfirmationModal
        isOpen={true}
        onClose={handleClose}
        duplicateInfo={currentDuplicate}
        onConfirm={handleConfirm}
        onSkip={handleSkip}
      />
    </>
  );
}
