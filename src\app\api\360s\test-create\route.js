import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// POST /api/360s/test-create - Simple test endpoint for 360° creation
export async function POST(request) {
  try {
    console.log('Test 360° creation endpoint called');
    
    const body = await request.json();
    console.log('Test request body:', body);
    
    // Just return success without database operations for testing
    return NextResponse.json({
      success: true,
      message: 'Test endpoint working',
      receivedData: body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// GET /api/360s/test-create - Test database connection
export async function GET(request) {
  try {
    console.log('Testing database connection...');
    
    await connectDB();
    console.log('Database connected successfully');
    
    // Try to count documents
    const count = await _360Settings.countDocuments();
    console.log('Document count:', count);
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      documentCount: count,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
