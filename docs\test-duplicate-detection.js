// Test script for 360° duplicate detection functionality
// Run this in browser console to test the duplicate detection API

async function testDuplicateDetection() {
  console.log('🧪 Testing 360° Duplicate Detection System...');
  
  try {
    // 1. Get existing 360° images to use for testing
    console.log('📋 Fetching existing 360° images...');
    const listResponse = await fetch('/api/360s');
    const listData = await listResponse.json();
    
    if (!listData.success || !listData.data.length) {
      console.error('❌ No existing 360° images found. Please create some first.');
      return;
    }
    
    const existingImages = listData.data;
    console.log(`✅ Found ${existingImages.length} existing images`);
    
    // 2. Test duplicate detection with existing filenames
    const testFilenames = [
      existingImages[0].name + '.jpg', // This should be a duplicate
      'new-test-image-1.jpg',          // This should be new
      'new-test-image-2.png',          // This should be new
    ];
    
    // Add another duplicate if we have multiple images
    if (existingImages.length > 1) {
      testFilenames.push(existingImages[1].name + '.tiff'); // Another duplicate
    }
    
    console.log('🔍 Testing duplicate detection with filenames:', testFilenames);
    
    const duplicateResponse = await fetch('/api/360s/check-duplicates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filenames: testFilenames }),
    });
    
    const duplicateData = await duplicateResponse.json();
    
    if (!duplicateResponse.ok) {
      console.error('❌ Duplicate check failed:', duplicateData);
      return;
    }
    
    console.log('✅ Duplicate check successful:', duplicateData);
    
    // 3. Analyze results
    const { duplicates, newFiles, summary } = duplicateData.data;
    
    console.log('📊 Results Analysis:');
    console.log(`   Total files checked: ${summary.totalFiles}`);
    console.log(`   Duplicates found: ${summary.duplicatesFound}`);
    console.log(`   New files: ${summary.newFilesCount}`);
    
    // 4. Verify duplicate detection accuracy
    console.log('🔍 Verifying duplicate detection accuracy...');
    
    duplicates.forEach((duplicate, index) => {
      console.log(`   Duplicate ${index + 1}:`);
      console.log(`     Filename: ${duplicate.filename}`);
      console.log(`     Name without ext: ${duplicate.nameWithoutExt}`);
      console.log(`     Existing ID: ${duplicate.existingData._id}`);
      console.log(`     Has markers: ${duplicate.existingData.hasMarkers}`);
      console.log(`     Has camera settings: ${duplicate.existingData.hasCameraSettings}`);
      console.log(`     Created: ${new Date(duplicate.existingData.createdAt).toLocaleString()}`);
    });
    
    newFiles.forEach((newFile, index) => {
      console.log(`   New file ${index + 1}: ${newFile.filename}`);
    });
    
    // 5. Test edge cases
    console.log('🧪 Testing edge cases...');
    
    // Test with empty array
    const emptyResponse = await fetch('/api/360s/check-duplicates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filenames: [] }),
    });
    
    const emptyData = await emptyResponse.json();
    console.log('   Empty array test:', emptyData.success ? '✅ Handled correctly' : '❌ Failed');
    
    // Test with invalid data
    const invalidResponse = await fetch('/api/360s/check-duplicates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filenames: 'not-an-array' }),
    });
    
    const invalidData = await invalidResponse.json();
    console.log('   Invalid data test:', !invalidData.success ? '✅ Rejected correctly' : '❌ Should have failed');
    
    // 6. Test filename variations
    console.log('🔤 Testing filename variations...');
    
    const variationFilenames = [
      existingImages[0].name + '.JPG',     // Different case extension
      existingImages[0].name + '.jpeg',    // Different extension
      existingImages[0].name.toUpperCase() + '.jpg', // Different case name
    ];
    
    const variationResponse = await fetch('/api/360s/check-duplicates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filenames: variationFilenames }),
    });
    
    const variationData = await variationResponse.json();
    console.log('   Filename variations test:', variationData);
    
    // 7. Performance test with many files
    console.log('⚡ Testing performance with many files...');
    
    const manyFilenames = [];
    for (let i = 0; i < 50; i++) {
      manyFilenames.push(`test-file-${i}.jpg`);
    }
    // Add a few duplicates
    manyFilenames.push(existingImages[0].name + '.jpg');
    if (existingImages.length > 1) {
      manyFilenames.push(existingImages[1].name + '.png');
    }
    
    const startTime = performance.now();
    const performanceResponse = await fetch('/api/360s/check-duplicates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ filenames: manyFilenames }),
    });
    const endTime = performance.now();
    
    const performanceData = await performanceResponse.json();
    console.log(`   Performance test: ${manyFilenames.length} files processed in ${(endTime - startTime).toFixed(2)}ms`);
    console.log(`   Found ${performanceData.data.duplicates.length} duplicates out of ${manyFilenames.length} files`);
    
    console.log('🎉 All duplicate detection tests completed successfully!');
    
    // 8. Test data structure validation
    console.log('🔍 Validating response data structure...');
    
    const requiredFields = ['success', 'data', 'message'];
    const dataFields = ['duplicates', 'newFiles', 'summary'];
    const duplicateFields = ['filename', 'nameWithoutExt', 'isDuplicate', 'existingData'];
    const existingDataFields = ['_id', 'name', 'url', 'originalFileName', 'hasMarkers', 'hasCameraSettings'];
    
    let structureValid = true;
    
    // Check main response structure
    requiredFields.forEach(field => {
      if (!(field in duplicateData)) {
        console.error(`❌ Missing required field: ${field}`);
        structureValid = false;
      }
    });
    
    // Check data structure
    dataFields.forEach(field => {
      if (!(field in duplicateData.data)) {
        console.error(`❌ Missing data field: ${field}`);
        structureValid = false;
      }
    });
    
    // Check duplicate structure
    if (duplicateData.data.duplicates.length > 0) {
      const firstDuplicate = duplicateData.data.duplicates[0];
      duplicateFields.forEach(field => {
        if (!(field in firstDuplicate)) {
          console.error(`❌ Missing duplicate field: ${field}`);
          structureValid = false;
        }
      });
      
      // Check existing data structure
      if (firstDuplicate.existingData) {
        existingDataFields.forEach(field => {
          if (!(field in firstDuplicate.existingData)) {
            console.error(`❌ Missing existing data field: ${field}`);
            structureValid = false;
          }
        });
      }
    }
    
    console.log(`   Data structure validation: ${structureValid ? '✅ Valid' : '❌ Invalid'}`);
    
    return {
      success: true,
      duplicatesFound: duplicateData.data.duplicates.length,
      newFilesFound: duplicateData.data.newFiles.length,
      performanceMs: endTime - startTime,
      structureValid
    };
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return { success: false, error: error.message };
  }
}

// Instructions for use
console.log(`
🧪 360° Duplicate Detection Test Suite

To run this test:
1. Open browser developer tools (F12)
2. Navigate to the 360° file manager page
3. Go to Console tab
4. Copy and paste this entire script
5. Run: testDuplicateDetection()

This will test:
- Basic duplicate detection functionality
- Edge cases and error handling
- Performance with many files
- Data structure validation
- Filename variation handling

Make sure you have at least one existing 360° image in the database before running.
`);

// Uncomment to run automatically
// testDuplicateDetection().then(result => console.log('Test completed:', result));
