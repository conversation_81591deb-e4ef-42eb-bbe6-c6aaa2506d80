#!/bin/bash

# Fix Production Environment Variables
# This script helps fix the Stripe environment variable issue in production

echo "🔧 Fixing Production Environment Variables..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📋 Production Environment Fix for Stripe Integration${NC}"
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: Run this script from the project root directory${NC}"
    exit 1
fi

# Check if .env.local exists (development file)
if [ ! -f ".env.local" ]; then
    echo -e "${RED}❌ Error: .env.local file not found${NC}"
    exit 1
fi

echo -e "${YELLOW}📝 Step 1: Backing up current .env.production...${NC}"
if [ -f ".env.production" ]; then
    cp .env.production .env.production.backup
    echo -e "${GREEN}✅ Backup created: .env.production.backup${NC}"
fi

echo -e "${YELLOW}📝 Step 2: Creating corrected .env.production...${NC}"

# Extract values from .env.local and create production file
cat > .env.production << 'EOF'
# Production Environment Configuration for victorchelemu.com

# Auth.js Configuration - PRODUCTION
NEXTAUTH_URL=https://victorchelemu.com
NEXTAUTH_SECRET="90y62xKLcF2XRkbsJMxqfl0WLGx48usjU47ks58h0xY"

# OAuth Providers - PRODUCTION
GOOGLE_CLIENT_ID="1090336007340-vib1q42r4bpqn81dtqqa8ml06c7bb903.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

FACEBOOK_CLIENT_ID="1861902804631361"
FACEBOOK_CLIENT_SECRET="********************************"

# Legacy OAuth variables (kept for reference)
AUTH_GOOGLE_ID="1090336007340-vib1q42r4bpqn81dtqqa8ml06c7bb903.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET="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"
AUTH_FACEBOOK_ID="1861902804631361"
AUTH_FACEBOOK_SECRET="********************************"

# Email Configuration (for magic links)
EMAIL_SERVER_HOST=smtp.hostinger.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=k@6vW@fFatBbW?Y
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Luyari

# MongoDB Configuration - PRODUCTION
MONGODB_URI="mongodb+srv://luyariAdmin:<EMAIL>/elephantisland?retryWrites=true&w=majority&appName=appsDb"

# Stripe Configuration - PRODUCTION (Using test keys for now)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51QYOwZHh2D2bp9dNMuR5Efxv7Rh5O7qEocezuVcniVzH01X1MKwfED4MksvmFTXfBPnn7CZF7skOgZ9UIOFMYvNs00RQ0bt07a"
STRIPE_SECRET_KEY="sk_test_51QYOwZHh2D2bp9dNufpPael7PuJbsxsY7RNHxMF2KD2WPBudFpbQtBzoGyacFZLxOIzOZEFpFZt934wCd0iKI3fV00sP2TMLhm"
STRIPE_WEBHOOK_SECRET="whsec_1234567890abcdef1234567890abcdef12345678"

# Application Configuration - PRODUCTION
NEXT_PUBLIC_APP_URL=https://victorchelemu.com

# Admin Email Configuration
ADMIN_EMAIL=<EMAIL>

# Production Environment
NODE_ENV=production
EOF

echo -e "${GREEN}✅ Production environment file created with correct values${NC}"

echo ""
echo -e "${BLUE}📋 Next Steps for Production Server:${NC}"
echo ""
echo -e "${YELLOW}1. Upload the corrected .env.production to your server:${NC}"
echo "   scp .env.production <EMAIL>:/home/<USER>/htdocs/victorchelemu.com/elephantisland/"
echo ""
echo -e "${YELLOW}2. SSH into your production server:${NC}"
echo "   ssh <EMAIL>"
echo ""
echo -e "${YELLOW}3. Navigate to the application directory:${NC}"
echo "   cd /home/<USER>/htdocs/victorchelemu.com/elephantisland/"
echo ""
echo -e "${YELLOW}4. Copy the environment file:${NC}"
echo "   cp .env.production .env.local"
echo ""
echo -e "${YELLOW}5. Restart the application:${NC}"
echo "   pm2 restart elephant"
echo "   pm2 logs elephant"
echo ""
echo -e "${GREEN}✅ Environment fix completed!${NC}"
echo -e "${BLUE}🔍 Test the fix by visiting: https://victorchelemu.com/api/debug/env${NC}"
