import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// POST /api/360s/check-duplicates - Check for duplicate filenames
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const { filenames } = body;
    
    if (!Array.isArray(filenames) || filenames.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'filenames must be a non-empty array',
        },
        { status: 400 }
      );
    }
    
    // Extract names without extensions for comparison
    const namesToCheck = filenames.map(filename => 
      filename.replace(/\.[^/.]+$/, '') // Remove extension
    );
    
    // Find existing 360° images with matching names
    const existingImages = await _360Settings.find({
      name: { $in: namesToCheck }
    }).select('_id name url originalFileName markerList cameraPosition _360Rotation createdAt updatedAt');
    
    // Create a map of existing names to their data
    const existingMap = {};
    existingImages.forEach(image => {
      existingMap[image.name] = {
        _id: image._id,
        name: image.name,
        url: image.url,
        originalFileName: image.originalFileName,
        hasMarkers: Array.isArray(image.markerList) && image.markerList.length > 0,
        hasCameraSettings: image.cameraPosition !== -0.0001 || image._360Rotation !== -0.0001,
        createdAt: image.createdAt,
        updatedAt: image.updatedAt
      };
    });
    
    // Check each filename for duplicates
    const duplicateResults = filenames.map(filename => {
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
      const existing = existingMap[nameWithoutExt];
      
      return {
        filename,
        nameWithoutExt,
        isDuplicate: !!existing,
        existingData: existing || null
      };
    });
    
    // Separate duplicates from new files
    const duplicates = duplicateResults.filter(result => result.isDuplicate);
    const newFiles = duplicateResults.filter(result => !result.isDuplicate);
    
    console.log('Duplicate check results:', {
      totalFiles: filenames.length,
      duplicatesFound: duplicates.length,
      newFiles: newFiles.length,
      duplicates: duplicates.map(d => ({ 
        filename: d.filename, 
        existingId: d.existingData?._id,
        hasMarkers: d.existingData?.hasMarkers,
        hasCameraSettings: d.existingData?.hasCameraSettings
      }))
    });
    
    return NextResponse.json({
      success: true,
      data: {
        duplicates,
        newFiles,
        summary: {
          totalFiles: filenames.length,
          duplicatesFound: duplicates.length,
          newFilesCount: newFiles.length
        }
      },
      message: duplicates.length > 0 
        ? `Found ${duplicates.length} duplicate filename(s)`
        : 'No duplicates found'
    });
    
  } catch (error) {
    console.error('Error checking duplicates:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to check duplicates',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
