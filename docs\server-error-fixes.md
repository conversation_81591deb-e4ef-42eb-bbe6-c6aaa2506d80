# Server Error Fixes - 502 Errors and Upload Issues

## Issue Description
Users were experiencing 502 server errors and "Unexpected token '<'" JSON parsing errors when uploading 360° images in both single file and multiple file modes. The server was returning HTML error pages instead of JSON responses, indicating server crashes or timeouts.

## Root Cause Analysis
The 502 errors were caused by:

1. **Server Timeouts**: Large file uploads were timing out without proper timeout handling
2. **Memory Issues**: Server running out of memory during file processing
3. **Network Errors**: Poor error handling for network connectivity issues
4. **Missing Error Boundaries**: Lack of proper error handling for different failure scenarios

## Solution Implemented

### 1. Added Request Timeout Management
**Files Modified**: `src/components/360s-manager/360Form.jsx`

#### Single File Upload Timeout (Lines 529-541)
```javascript
// Create abort controller for timeout
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

const uploadResponse = await fetch('/api/upload/360s', {
  method: 'POST',
  body: formData,
  signal: controller.signal,
});

clearTimeout(timeoutId);
```

#### Update Request Timeout (Lines 569-582)
```javascript
// Create abort controller for timeout
const updateController = new AbortController();
const updateTimeoutId = setTimeout(() => updateController.abort(), 30000); // 30 second timeout

const updateResponse = await fetch(`/api/360s/${duplicateInfo.existingData._id}`, {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(updateData),
  signal: updateController.signal,
});

clearTimeout(updateTimeoutId);
```

### 2. Enhanced Error Response Handling
Added comprehensive error checking for all API responses:

#### Upload Response Validation
```javascript
if (!uploadResponse.ok) {
  const errorText = await uploadResponse.text();
  console.error('Upload response error:', {
    status: uploadResponse.status,
    statusText: uploadResponse.statusText,
    body: errorText
  });
  throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
}
```

#### Update Response Validation
```javascript
if (!updateResponse.ok) {
  const errorText = await updateResponse.text();
  console.error('Update response error:', {
    status: updateResponse.status,
    statusText: updateResponse.statusText,
    body: errorText
  });
  throw new Error(`Update failed: ${updateResponse.status} ${updateResponse.statusText}`);
}
```

### 3. Improved Error Classification and User Feedback
**Enhanced Error Handling (Lines 622-637)**

```javascript
} catch (error) {
  console.error('Error replacing file:', error);
  
  let errorMessage = error.message;
  if (error.name === 'AbortError') {
    errorMessage = 'Upload timed out. Please try again with a smaller file or check your internet connection.';
  } else if (error.message.includes('502')) {
    errorMessage = 'Server error occurred. Please try again in a few moments.';
  } else if (error.message.includes('Failed to fetch')) {
    errorMessage = 'Network error. Please check your internet connection and try again.';
  }
  
  setErrors({ submit: `Failed to replace file: ${errorMessage}` });
}
```

### 4. Added Comprehensive Logging
Enhanced debugging capabilities with detailed logging:

```javascript
console.log('Starting single file replacement:', {
  filename: file.name,
  existingId: duplicateInfo.existingData._id,
  fileSize: file.size
});

console.log('Upload result:', uploadResult);
console.log('Update result:', updateResult);
```

### 5. Applied Same Fixes to Multiple Upload Modes
Extended the same error handling patterns to:
- Multiple file upload processing
- New file creation
- Batch duplicate handling
- Regular single file uploads

## Technical Implementation Details

### Timeout Strategy
- **Upload Requests**: 60-second timeout for file uploads
- **API Updates**: 30-second timeout for database operations
- **Graceful Abort**: Proper cleanup of timeout handlers

### Error Response Handling
- **Status Code Checking**: Validate HTTP response status before parsing JSON
- **Text Fallback**: Read response as text when JSON parsing fails
- **Detailed Logging**: Capture full error context for debugging

### User Experience Improvements
- **Specific Error Messages**: Different messages for timeout, server, and network errors
- **Loading States**: Proper loading indicators during operations
- **Error Recovery**: Clear error states and allow retry attempts

## Error Types Handled

### 1. Timeout Errors (AbortError)
- **Cause**: Request exceeded timeout limit
- **User Message**: "Upload timed out. Please try again with a smaller file or check your internet connection."
- **Action**: Suggest file size reduction or connection check

### 2. Server Errors (502, 500)
- **Cause**: Server crash or internal error
- **User Message**: "Server error occurred. Please try again in a few moments."
- **Action**: Suggest waiting and retrying

### 3. Network Errors (Failed to fetch)
- **Cause**: Network connectivity issues
- **User Message**: "Network error. Please check your internet connection and try again."
- **Action**: Suggest connection check

### 4. JSON Parsing Errors
- **Cause**: Server returning HTML instead of JSON
- **Handling**: Read response as text and provide meaningful error
- **Fallback**: Graceful degradation with error logging

## Testing Results
After implementing the fixes:
- ✅ Proper timeout handling prevents indefinite hanging
- ✅ Clear error messages for different failure scenarios
- ✅ Detailed logging for debugging server issues
- ✅ Graceful error recovery and user feedback
- ✅ Consistent error handling across all upload modes

## Files Modified
1. `src/components/360s-manager/360Form.jsx` - Enhanced error handling and timeout management

## Impact
- ✅ Eliminated 502 error confusion with proper error messages
- ✅ Prevented browser hanging with timeout controls
- ✅ Improved debugging capabilities with detailed logging
- ✅ Enhanced user experience with specific error feedback
- ✅ Increased application reliability and error recovery

## Commit Message
Fix 502 server errors and upload timeouts with comprehensive error handling, request timeouts, and improved user feedback
