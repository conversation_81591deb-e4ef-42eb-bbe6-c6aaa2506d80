'use client';

import React from 'react';

/**
 * UI Asset Caching System for 360° Viewer Components
 * Provides comprehensive image caching for thumbnails, icons, and interface graphics
 * Optimized for performance and user experience
 */

// UI Asset cache to prevent repeated requests
const uiAssetCache = new Map();
const loadingPromises = new Map();
const preloadQueue = new Set();

// Configuration for UI assets
const UI_ASSET_CONFIG = {
  maxRetries: 2,
  retryDelay: 500,
  cacheTimeout: 10 * 60 * 1000, // 10 minutes for UI assets
  requestTimeout: 5000, // 5 seconds for UI assets
  preloadBatchSize: 5,
};

/**
 * Enhanced UI image loading with caching and error handling
 */
export async function loadUIImageWithCache(src, options = {}) {
  if (!src) {
    throw new Error('No source provided for UI image');
  }

  const config = { ...UI_ASSET_CONFIG, ...options };
  
  // Check if already cached
  if (uiAssetCache.has(src)) {
    const cached = uiAssetCache.get(src);
    if (Date.now() - cached.timestamp < config.cacheTimeout) {
      return cached.image;
    } else {
      // Remove expired cache entry
      uiAssetCache.delete(src);
    }
  }

  // Check if already loading
  if (loadingPromises.has(src)) {
    return loadingPromises.get(src);
  }

  // Create loading promise
  const loadingPromise = loadUIImageWithRetry(src, config);
  loadingPromises.set(src, loadingPromise);

  try {
    const image = await loadingPromise;
    
    // Cache the loaded image
    uiAssetCache.set(src, {
      image,
      timestamp: Date.now(),
      size: image.naturalWidth * image.naturalHeight * 4, // Approximate size
    });

    return image;
  } finally {
    loadingPromises.delete(src);
  }
}

/**
 * Load UI image with retry logic
 */
async function loadUIImageWithRetry(src, config) {
  let lastError;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await loadUIImageDirect(src, config.requestTimeout);
    } catch (error) {
      lastError = error;
      
      if (attempt < config.maxRetries) {
        // Wait before retry with exponential backoff
        const delay = config.retryDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

/**
 * Direct UI image loading with timeout
 */
function loadUIImageDirect(src, timeout) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    const timeoutId = setTimeout(() => {
      img.onload = null;
      img.onerror = null;
      reject(new Error(`UI image loading timeout: ${src}`));
    }, timeout);

    img.onload = () => {
      clearTimeout(timeoutId);
      resolve(img);
    };

    img.onerror = () => {
      clearTimeout(timeoutId);
      reject(new Error(`Failed to load UI image: ${src}`));
    };

    // Set cross-origin for local assets
    img.crossOrigin = 'anonymous';
    img.src = src;
  });
}

/**
 * Preload UI assets in batches for better performance
 */
export function preloadUIAssets(assetUrls) {
  if (!Array.isArray(assetUrls)) {
    console.warn('preloadUIAssets expects an array of URLs');
    return;
  }

  // Add to preload queue
  assetUrls.forEach(url => {
    if (url && !uiAssetCache.has(url)) {
      preloadQueue.add(url);
    }
  });

  // Process preload queue
  processPreloadQueue();
}

/**
 * Process preload queue in batches
 */
async function processPreloadQueue() {
  if (preloadQueue.size === 0) return;

  const batch = Array.from(preloadQueue).slice(0, UI_ASSET_CONFIG.preloadBatchSize);
  
  // Remove from queue
  batch.forEach(url => preloadQueue.delete(url));

  // Load batch concurrently
  const loadPromises = batch.map(async (url) => {
    try {
      await loadUIImageWithCache(url);
    } catch (error) {
      console.warn(`Failed to preload UI asset: ${url}`, error);
    }
  });

  await Promise.allSettled(loadPromises);

  // Continue with next batch if queue not empty
  if (preloadQueue.size > 0) {
    // Use requestIdleCallback for better performance
    if (typeof window !== 'undefined' && window.requestIdleCallback) {
      window.requestIdleCallback(() => processPreloadQueue(), { timeout: 1000 });
    } else {
      setTimeout(processPreloadQueue, 100);
    }
  }
}

/**
 * Get UI cache statistics
 */
export function getUICacheStats() {
  const totalSize = Array.from(uiAssetCache.values())
    .reduce((sum, item) => sum + (item.size || 0), 0);

  return {
    cacheSize: uiAssetCache.size,
    loadingCount: loadingPromises.size,
    preloadQueueSize: preloadQueue.size,
    totalMemoryUsage: totalSize,
    memoryUsageMB: (totalSize / (1024 * 1024)).toFixed(2),
  };
}

/**
 * Clear UI asset cache
 */
export function clearUIAssetCache(specificUrl = null) {
  if (specificUrl) {
    uiAssetCache.delete(specificUrl);
    loadingPromises.delete(specificUrl);
    preloadQueue.delete(specificUrl);
  } else {
    uiAssetCache.clear();
    loadingPromises.clear();
    preloadQueue.clear();
  }
}

/**
 * Preload common 360° viewer UI assets
 */
export function preload360ViewerAssets() {
  const common360Assets = [
    '/assets/elephant_island_logo.png',
    '/assets/elephant_island_logo_white.png',
    '/assets/swipe_icon.png',
    '/assets/guide_btn_off.png',
    '/assets/guide_btn_ov.png',
    '/assets/upstairs_btn_off.png',
    '/assets/upstairs_btn_ov.png',
    '/assets/downstairs_btn_off.png',
    '/assets/downstairs_btn_ov.png',
    '/assets/home_btn_off.png',
    '/assets/home_btn_ov.png',
    '/assets/entrance_btn_off.png',
    '/assets/entrance_btn_ov.png',
  ];

  preloadUIAssets(common360Assets);
}

/**
 * React hook for UI asset loading with caching
 */
export function useUIAssetCache(src, options = {}) {
  const [state, setState] = React.useState({
    loading: true,
    error: null,
    image: null,
  });

  React.useEffect(() => {
    if (!src) {
      setState({ loading: false, error: new Error('No source provided'), image: null });
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    loadUIImageWithCache(src, options)
      .then(image => {
        setState({ loading: false, error: null, image });
      })
      .catch(error => {
        setState({ loading: false, error, image: null });
      });
  }, [src, options]);

  return state;
}

// Auto-preload common assets when module loads
if (typeof window !== 'undefined') {
  // Delay preloading to not interfere with initial page load
  setTimeout(() => {
    preload360ViewerAssets();
  }, 2000);
}
