#!/bin/bash

# Production Deployment Script for Elephant Island Lodge
# Server: victorchelemu.com
# Path: /home/<USER>/htdocs/victorchelemu.com/elephantisland/

echo "🚀 Starting production deployment for Elephant Island Lodge..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Production server details
PROD_SERVER="victorchelemu.com"
PROD_PATH="/home/<USER>/htdocs/victorchelemu.com/elephantisland"
PM2_APP_NAME="elephant"

echo -e "${BLUE}📋 Deployment Configuration:${NC}"
echo -e "  Server: ${PROD_SERVER}"
echo -e "  Path: ${PROD_PATH}"
echo -e "  PM2 App: ${PM2_APP_NAME}"
echo ""

# Step 1: Check if production environment file exists
echo -e "${YELLOW}📝 Step 1: Checking production environment configuration...${NC}"

if [ ! -f ".env.production" ]; then
    echo -e "${YELLOW}Creating production environment file with placeholders...${NC}"
    echo -e "${RED}⚠️  WARNING: Update .env.production with real values before deployment!${NC}"

    cat > .env.production << 'EOF'
# Production Environment Configuration for victorchelemu.com

# Auth.js Configuration - PRODUCTION
NEXTAUTH_URL=https://victorchelemu.com
NEXTAUTH_SECRET="*****"

# OAuth Providers - PRODUCTION
GOOGLE_CLIENT_ID="*****"
GOOGLE_CLIENT_SECRET="*****"

FACEBOOK_CLIENT_ID="*****"
FACEBOOK_CLIENT_SECRET="*****"

# Email Configuration
EMAIL_SERVER_HOST=smtp.hostinger.com
EMAIL_SERVER_PORT=465
EMAIL_SERVER_USER=*****
EMAIL_SERVER_PASSWORD=*****
EMAIL_FROM=*****
EMAIL_FROM_NAME=Luyari

# MongoDB Configuration - PRODUCTION
MONGODB_URI="mongodb+srv://*****:*****@*****.mongodb.net/elephantisland?retryWrites=true&w=majority&appName=*****"

# Stripe Configuration - PRODUCTION
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="*****"
STRIPE_SECRET_KEY="*****"
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Application Configuration - PRODUCTION
NEXT_PUBLIC_APP_URL=https://victorchelemu.com

# Admin Email Configuration
ADMIN_EMAIL=<EMAIL>

# Production Environment
NODE_ENV=production
EOF
else
    echo -e "${GREEN}✅ Production environment file already exists${NC}"
fi

echo -e "${GREEN}✅ Production environment file created${NC}"

# Step 2: Build the application
echo -e "${YELLOW}🔨 Step 2: Building application...${NC}"
npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build completed successfully${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Step 3: Display deployment instructions
echo ""
echo -e "${BLUE}📋 Manual Deployment Steps for Production Server:${NC}"
echo ""
echo -e "${YELLOW}1. Upload files to production server:${NC}"
echo "   rsync -avz --exclude node_modules --exclude .git ./ user@${PROD_SERVER}:${PROD_PATH}/"
echo ""
echo -e "${YELLOW}2. SSH into production server:${NC}"
echo "   ssh user@${PROD_SERVER}"
echo ""
echo -e "${YELLOW}3. Navigate to application directory:${NC}"
echo "   cd ${PROD_PATH}"
echo ""
echo -e "${YELLOW}4. Install dependencies:${NC}"
echo "   npm install --production"
echo ""
echo -e "${YELLOW}5. Copy environment file:${NC}"
echo "   cp .env.production .env.local"
echo -e "${RED}   ⚠️  IMPORTANT: Ensure .env.production has real values, not placeholders!${NC}"
echo ""
echo -e "${YELLOW}6. Build application (if not done locally):${NC}"
echo "   npm run build"
echo ""
echo -e "${YELLOW}7. Restart PM2 application:${NC}"
echo "   pm2 restart ${PM2_APP_NAME}"
echo "   pm2 logs ${PM2_APP_NAME}"
echo ""

# Step 4: OAuth Provider Configuration Instructions
echo -e "${BLUE}🔐 OAuth Provider Configuration Required:${NC}"
echo ""
echo -e "${YELLOW}Google OAuth Console (https://console.cloud.google.com/):${NC}"
echo "   1. Navigate to APIs & Services > Credentials"
echo "   2. Edit your OAuth 2.0 Client ID"
echo "   3. Add to Authorized redirect URIs:"
echo "      - https://${PROD_SERVER}/api/auth/callback/google"
echo "   4. Add to Authorized JavaScript origins:"
echo "      - https://${PROD_SERVER}"
echo ""
echo -e "${YELLOW}Facebook OAuth Console (https://developers.facebook.com/):${NC}"
echo "   1. Navigate to your app > Facebook Login > Settings"
echo "   2. Add to Valid OAuth Redirect URIs:"
echo "      - https://${PROD_SERVER}/api/auth/callback/facebook"
echo ""

# Step 5: Testing instructions
echo -e "${BLUE}🧪 Testing Instructions:${NC}"
echo ""
echo -e "${YELLOW}Test authentication endpoints:${NC}"
echo "   curl -I https://${PROD_SERVER}/api/auth/providers"
echo "   curl -I https://${PROD_SERVER}/api/auth/session"
echo "   curl -I https://${PROD_SERVER}/auth/signin"
echo ""
echo -e "${YELLOW}Test admin dashboard:${NC}"
echo "   https://${PROD_SERVER}/admin/dashboard"
echo ""

# Step 6: Monitoring
echo -e "${BLUE}📊 Monitoring Commands:${NC}"
echo ""
echo -e "${YELLOW}Check PM2 status:${NC}"
echo "   pm2 status"
echo "   pm2 logs ${PM2_APP_NAME}"
echo ""
echo -e "${YELLOW}Check application health:${NC}"
echo "   curl -I https://${PROD_SERVER}/api/test-mongodb"
echo ""

echo -e "${GREEN}🎉 Deployment preparation completed!${NC}"
echo -e "${BLUE}📋 Next: Follow the manual deployment steps above${NC}"
echo ""
echo -e "${YELLOW}⚠️  Important Notes:${NC}"
echo "   - Ensure OAuth providers are configured with production URLs"
echo "   - Verify MongoDB connection from production server"
echo "   - Test authentication flow after deployment"
echo "   - Monitor PM2 logs for any errors"
echo ""
echo -e "${GREEN}✅ Ready for production deployment!${NC}"
