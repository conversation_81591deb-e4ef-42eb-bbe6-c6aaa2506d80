import { NextResponse } from 'next/server';

// GET /api/debug/env - Debug environment variables (remove in production)
export async function GET() {
  try {
    const envDebug = {
      NODE_ENV: process.env.NODE_ENV,
      // Only show public env vars and whether private ones are set
      NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'SET' : 'NOT SET',
      STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY ? 'SET' : 'NOT SET',
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ? 'SET' : 'NOT SET',
      MONGODB_URI: process.env.MONGODB_URI ? 'SET' : 'NOT SET',
      // Count all env vars
      totalEnvVars: Object.keys(process.env).length,
      publicEnvVars: Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC_')).length,
      // Show first few characters of Stripe key for verification
      stripeKeyPreview: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 
        process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.substring(0, 20) + '...' : 'NOT SET'
    };

    return NextResponse.json({
      success: true,
      data: envDebug,
      message: 'Environment debug info retrieved'
    });
  } catch (error) {
    console.error('Error in env debug:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Server Error',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
